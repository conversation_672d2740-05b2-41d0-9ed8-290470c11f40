package com.ainative.mountainsurvival

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.ainative.mountainsurvival.databinding.ActivityStartBinding

/**
 * 游戏开始界面
 * 显示游戏标题、描述和开始/退出按钮
 */
class StartActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "StartActivity"
    }
    
    private lateinit var binding: ActivityStartBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "创建开始界面")
        
        // 初始化视图绑定
        binding = ActivityStartBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 隐藏ActionBar
        supportActionBar?.hide()
        
        // 设置按钮点击监听器
        setupClickListeners()
        
        Log.d(TAG, "开始界面初始化完成")
    }
    
    /**
     * 设置按钮点击监听器
     */
    private fun setupClickListeners() {
        // 开始游戏按钮
        binding.startGameButton.setOnClickListener {
            Log.d(TAG, "用户点击开始游戏")
            startGame()
        }
        
        // 退出游戏按钮
        binding.exitGameButton.setOnClickListener {
            Log.d(TAG, "用户点击退出游戏")
            exitGame()
        }
    }
    
    /**
     * 开始游戏
     * 跳转到主游戏界面
     */
    private fun startGame() {
        Log.d(TAG, "启动游戏，跳转到MainActivity")
        
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        
        // 关闭开始界面
        finish()
    }
    
    /**
     * 退出游戏
     * 关闭应用程序
     */
    private fun exitGame() {
        Log.d(TAG, "退出游戏")
        
        // 关闭应用程序
        finish()
    }
    
    override fun onBackPressed() {
        // 在开始界面按返回键直接退出应用
        Log.d(TAG, "用户按返回键，退出应用")
        super.onBackPressed()
        finish()
    }
}
